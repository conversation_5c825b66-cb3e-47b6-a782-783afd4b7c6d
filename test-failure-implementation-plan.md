# Test Failure Resolution Implementation Plan

**Document Version:** 1.0  
**Date:** 2025-08-07  
**Phase:** Task Planning (Phase 2)  
**Methodology:** 5-Phase Implementation Framework

---

## Executive Summary

This implementation plan translates the root cause analysis findings into concrete, actionable work batches. Each batch
is designed as a ~30-minute focused work unit following the project's engineering-grade quality standards and Zero
Tolerance Policies.

**Total Estimated Effort:** 8-10 hours across 16 work batches  
**Critical Path:** Repository Layer Fixes → Service Layer Standardization → Test Infrastructure Enhancement  
**Success Criteria:** 95%+ test pass rate with 100% architectural compliance

---

## Implementation Strategy

### Batch Sequencing Rationale

1. **Infrastructure-First Approach**: Fix foundational issues before addressing dependent problems
2. **Risk Mitigation**: Address critical path items early to minimize downstream impact
3. **Incremental Validation**: Each batch includes verification steps to ensure progress
4. **Rollback Safety**: Each batch is independently reversible if issues arise

### Quality Gates Per Batch

- [ ] MyPy compliance maintained (100%)
- [ ] Ruff linting passes (zero errors)
- [ ] Type hints complete for all changes
- [ ] Unified error handling patterns applied
- [ ] Performance monitoring decorators included
- [ ] Test execution validates changes

---

## BATCH A: Critical Repository Layer Fixes (Priority: CRITICAL)

### A1: TaskRepository Field Name Correction

**Estimated Time:** 20 minutes  
**Risk Level:** Low  
**Dependencies:** None

**Objective:** Fix AttributeError in TaskRepository.search_tasks() method

**Tasks:**

1. **Code Fix** (10 min)

   - Open `server/src/core/repositories/general/task_repository.py`
   - Line 195: Change `self.model.title.ilike()` to `self.model.name.ilike()`
   - Verify no other references to `title` field in TaskRepository

2. **Validation** (10 min)
   - Run MyPy: `uv run mypy src/core/repositories/general/task_repository.py`
   - Run Ruff: `uv run ruff check src/core/repositories/general/task_repository.py`
   - Execute specific test:
     `uv run pytest tests/core/repositories/test_task_repository.py::TestTaskRepository::test_search_tasks -v`

**Success Criteria:**

- [ ] No AttributeError on .ilike method
- [ ] MyPy passes without errors
- [ ] Ruff linting clean
- [ ] Search functionality works correctly

### A2: Component Repository Query Optimization

**Estimated Time:** 25 minutes  
**Risk Level:** Medium  
**Dependencies:** A1 completion

**Objective:** Resolve MultipleResultsFound errors in ComponentCategoryRepository

**Tasks:**

1. **Analysis** (10 min)

   - Review `server/src/core/repositories/general/component_category_repository.py`
   - Identify queries using `scalar_one()` that should use `scalar_one_or_none()`
   - Check for missing unique constraints in queries

2. **Code Fix** (10 min)

   - Update query methods to use appropriate scalar methods
   - Add additional filtering criteria where needed
   - Ensure proper error handling for multiple results

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/repositories/test_component_category_repository.py -v`
   - Verify no MultipleResultsFound exceptions

**Success Criteria:**

- [ ] No MultipleResultsFound errors
- [ ] Query specificity improved
- [ ] Error handling properly implemented

---

## BATCH B: DateTime Standardization (Priority: HIGH)

### B1: Synchronization Service DateTime Fix

**Estimated Time:** 30 minutes  
**Risk Level:** Medium  
**Dependencies:** None

**Objective:** Resolve naive vs aware datetime mixing in SynchronizationService

**Tasks:**

1. **Utility Enhancement** (15 min)

   - Add `ensure_timezone_aware()` function to `src/core/utils/datetime_utils.py`
   - Implement consistent datetime comparison logic
   - Add timezone validation helpers

2. **Service Update** (10 min)

   - Update `server/src/core/services/general/synchronization_service.py`
   - Lines 1465-1472: Standardize datetime parsing
   - Apply timezone-aware datetime throughout service

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/services/test_synchronization_service_conflict_resolution.py -v`
   - Verify no TypeError on datetime comparisons

**Success Criteria:**

- [ ] All datetime objects are timezone-aware
- [ ] No TypeError in datetime comparisons
- [ ] Consistent timezone handling throughout service

### B2: Model DateTime Field Standardization

**Estimated Time:** 25 minutes  
**Risk Level:** Low  
**Dependencies:** B1 completion

**Objective:** Ensure consistent datetime field usage across all models

**Tasks:**

1. **Audit** (10 min)

   - Review all model files in `src/core/models/general/`
   - Identify datetime fields and their timezone awareness
   - Document inconsistencies

2. **Standardization** (10 min)

   - Update model datetime fields to use timezone-aware defaults
   - Apply `utcnow_aware()` where appropriate
   - Ensure migration compatibility

3. **Validation** (5 min)
   - Run model tests: `uv run pytest tests/core/models/ -v`
   - Verify datetime field behavior

**Success Criteria:**

- [ ] All model datetime fields use timezone-aware defaults
- [ ] Migration compatibility maintained
- [ ] Model tests pass

---

## BATCH C: Async/Await Pattern Standardization (Priority: HIGH)

### C1: UserService AsyncMock Configuration

**Estimated Time:** 30 minutes  
**Risk Level:** Medium  
**Dependencies:** None

**Objective:** Fix TypeError with MagicMock and await expressions in UserService tests

**Tasks:**

1. **Test Infrastructure Update** (15 min)

   - Create standardized AsyncMock fixtures in `tests/core/services/conftest.py`
   - Implement proper async service mock patterns
   - Add async repository mock configurations

2. **UserService Test Fix** (10 min)

   - Update `tests/core/services/test_user_service.py`
   - Apply standardized AsyncMock patterns
   - Fix await expression issues

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/services/test_user_service.py -v`
   - Verify all async operations work correctly

**Success Criteria:**

- [ ] No TypeError with await expressions
- [ ] AsyncMock properly configured
- [ ] All UserService tests pass

### C2: Service Layer Async Pattern Audit

**Estimated Time:** 25 minutes  
**Risk Level:** Low  
**Dependencies:** C1 completion

**Objective:** Ensure consistent async patterns across all service tests

**Tasks:**

1. **Audit** (15 min)

   - Review all service test files in `tests/core/services/`
   - Identify async pattern inconsistencies
   - Document required changes

2. **Standardization** (10 min)
   - Apply consistent AsyncMock patterns
   - Update fixture configurations
   - Ensure proper async/await usage

**Success Criteria:**

- [ ] Consistent async patterns across all service tests
- [ ] Proper AsyncMock configuration
- [ ] No async-related test failures

---

## BATCH D: Database Constraint and Migration Fixes (Priority: HIGH)

### D1: Test Data Factory Implementation

**Estimated Time:** 35 minutes  
**Risk Level:** Medium  
**Dependencies:** A1, A2 completion

**Objective:** Resolve IntegrityError and constraint violations in tests

**Tasks:**

1. **Factory Creation** (20 min)

   - Create comprehensive test data factories in `tests/factories/`
   - Implement factories for Task, Component, User, Project entities
   - Ensure all required fields are populated
   - Add relationship handling

2. **Test Update** (10 min)

   - Update failing tests to use data factories
   - Replace manual entity creation with factory usage
   - Ensure constraint compliance

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/models/ -v`
   - Verify no IntegrityError exceptions

**Success Criteria:**

- [ ] Comprehensive test data factories implemented
- [ ] No IntegrityError in model tests
- [ ] All required fields properly populated

### D2: Migration Validation Enhancement

**Estimated Time:** 30 minutes  
**Risk Level:** Medium  
**Dependencies:** D1 completion

**Objective:** Fix migration rollback scenarios and validation issues

**Tasks:**

1. **Migration Test Fix** (15 min)

   - Review `tests/core/database/test_migration_rollback_scenarios.py`
   - Fix NameError issues with variable scope
   - Add missing import statements

2. **Alembic Validation** (10 min)

   - Update `tests/core/database/test_alembic_migration_automation.py`
   - Fix table constraint validation logic
   - Ensure proper post-migration verification

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/database/ -v`
   - Verify migration tests pass

**Success Criteria:**

- [ ] No NameError in migration tests
- [ ] Proper table constraint validation
- [ ] All migration tests pass

---

## BATCH E: Connection Manager Integration Fixes (Priority: MEDIUM)

### E1: Mock Lifecycle Management

**Estimated Time:** 30 minutes  
**Risk Level:** Medium  
**Dependencies:** None

**Objective:** Fix AssertionError failures in connection manager integration tests

**Tasks:**

1. **Mock Enhancement** (20 min)

   - Update `tests/core/database/test_connection_manager_integration.py`
   - Implement proper mock lifecycle management
   - Fix engine disposal and cleanup issues
   - Enhance session factory caching tests

2. **Fixture Improvement** (5 min)

   - Create enhanced connection manager fixtures
   - Ensure proper mock state management
   - Add cleanup procedures

3. **Validation** (5 min)
   - Run: `uv run pytest tests/core/database/test_connection_manager_integration.py -v`
   - Verify all assertions pass

**Success Criteria:**

- [ ] No AssertionError in connection manager tests
- [ ] Proper mock lifecycle management
- [ ] Engine caching works correctly

---

## BATCH F: Final Integration and Validation (Priority: MEDIUM)

### F1: Comprehensive Test Suite Execution

**Estimated Time:** 25 minutes  
**Risk Level:** Low  
**Dependencies:** All previous batches

**Objective:** Validate all fixes work together and achieve target pass rate

**Tasks:**

1. **Full Test Run** (15 min)

   - Execute complete test suite: `uv run pytest tests/ --html=test-report-all.html`
   - Monitor pass rate and identify any remaining failures
   - Document results

2. **Performance Validation** (5 min)

   - Run performance tests: `uv run pytest -v -m performance`
   - Ensure no performance regressions

3. **Quality Gate Verification** (5 min)
   - Run MyPy: `uv run mypy src/ --show-error-codes`
   - Run Ruff: `uv run ruff check .`
   - Verify 100% compliance

**Success Criteria:**

- [ ] 95%+ test pass rate achieved
- [ ] No performance regressions
- [ ] 100% MyPy compliance
- [ ] Zero Ruff errors

### F2: Documentation and Handover

**Estimated Time:** 20 minutes  
**Risk Level:** Low  
**Dependencies:** F1 completion

**Objective:** Complete documentation and prepare for handover

**Tasks:**

1. **Documentation Update** (10 min)

   - Update TESTING.md with new patterns
   - Document resolved issues
   - Add troubleshooting guidance

2. **Handover Preparation** (10 min)
   - Create implementation summary
   - Document lessons learned
   - Prepare for next development phase

**Success Criteria:**

- [ ] Documentation updated
- [ ] Implementation summary complete
- [ ] Ready for handover

---

## Risk Management and Contingency Plans

### High-Risk Scenarios

1. **Database Schema Conflicts**

   - **Mitigation**: Test all changes in isolated environment first
   - **Rollback**: Maintain database backup before schema changes

2. **Async Pattern Deadlocks**

   - **Mitigation**: Implement comprehensive async testing
   - **Rollback**: Revert to previous async patterns if issues arise

3. **Performance Degradation**
   - **Mitigation**: Run performance tests after each batch
   - **Rollback**: Optimize or revert changes causing degradation

### Contingency Procedures

- **Batch Failure**: Isolate and fix individual batch before proceeding
- **Integration Issues**: Run targeted integration tests between batches
- **Time Overrun**: Prioritize critical path items, defer non-essential fixes

---

## Success Validation Framework

### Immediate Validation (Per Batch)

- [ ] Batch-specific tests pass
- [ ] No new failures introduced
- [ ] Quality gates maintained

### Integration Validation (Post-Batch Groups)

- [ ] Cross-batch functionality works
- [ ] No regression in related areas
- [ ] Performance maintained

### Final Validation (Project Completion)

- [ ] 95%+ overall test pass rate
- [ ] Zero tolerance policy compliance
- [ ] Architectural standards maintained
- [ ] Documentation complete

## Execution Timeline and Dependencies

### Critical Path Analysis

```
A1 (20min) → A2 (25min) → D1 (35min) → D2 (30min) → F1 (25min) → F2 (20min)
Total Critical Path: 155 minutes (2.6 hours)
```

### Parallel Execution Opportunities

```
Batch B (DateTime): B1 (30min) → B2 (25min) [55min total]
Batch C (Async): C1 (30min) → C2 (25min) [55min total]
Batch E (Connection): E1 (30min) [30min total]
```

**Optimal Execution Strategy:**

- **Phase 1** (Parallel): Execute Batches A, B, C simultaneously (45-55 minutes)
- **Phase 2** (Sequential): Execute Batches D, E (65 minutes)
- **Phase 3** (Final): Execute Batch F (45 minutes)
- **Total Optimized Time**: 155-165 minutes (2.6-2.8 hours)

### Dependency Matrix

| Batch | Prerequisites | Blocks | Can Run Parallel With |
| ----- | ------------- | ------ | --------------------- |
| A1    | None          | A2, D1 | B1, C1, E1            |
| A2    | A1            | D1     | B1, B2, C1, C2, E1    |
| B1    | None          | B2     | A1, A2, C1, E1        |
| B2    | B1            | None   | A2, C1, C2, E1        |
| C1    | None          | C2     | A1, A2, B1, E1        |
| C2    | C1            | None   | A2, B1, B2, E1        |
| D1    | A1, A2        | D2     | B2, C2, E1            |
| D2    | D1            | F1     | B2, C2, E1            |
| E1    | None          | None   | All except F1, F2     |
| F1    | All others    | F2     | None                  |
| F2    | F1            | None   | None                  |

---

## Technical Implementation Specifications

### Code Quality Standards (Applied to All Batches)

```python
# Required imports for all repository changes
from typing import Optional, List, Dict, Any
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.monitoring.unified_performance_monitor import monitor_repository_performance
```

### Batch A: Detailed Technical Specifications

#### A1: TaskRepository Field Correction

**File:** `server/src/core/repositories/general/task_repository.py` **Line:** 195

**Current Code:**

```python
conditions = [
    or_(self.model.title.ilike(f"%{search_term}%"), self.model.description.ilike(f"%{search_term}%")),
    self.model.is_deleted == False,
]
```

**Fixed Code:**

```python
conditions = [
    or_(self.model.name.ilike(f"%{search_term}%"), self.model.description.ilike(f"%{search_term}%")),
    self.model.is_deleted == False,
]
```

**Validation Commands:**

```bash
# Type checking
uv run mypy src/core/repositories/general/task_repository.py --show-error-codes

# Linting
uv run ruff check src/core/repositories/general/task_repository.py

# Specific test
uv run pytest tests/core/repositories/test_task_repository.py::TestTaskRepository::test_search_tasks -v
```

#### A2: Component Repository Query Optimization

**Files to Review:**

- `server/src/core/repositories/general/component_category_repository.py`
- `server/tests/core/repositories/test_component_category_repository.py`

**Common Fix Pattern:**

```python
# Change from:
result = await self.db_session.execute(stmt)
return result.scalar_one()  # Raises MultipleResultsFound

# To:
result = await self.db_session.execute(stmt)
entity = result.scalar_one_or_none()
if entity is None:
    raise NotFoundError(f"Entity not found")
return entity
```

### Batch B: DateTime Standardization Specifications

#### B1: Enhanced DateTime Utilities

**File:** `server/src/core/utils/datetime_utils.py`

**New Function to Add:**

```python
def ensure_timezone_aware(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """Ensure datetime object is timezone-aware.

    Args:
        dt: Datetime object, ISO string, or None

    Returns:
        Timezone-aware datetime or None

    Raises:
        ValueError: If string cannot be parsed
    """
    if dt is None:
        return None

    if isinstance(dt, str):
        # Handle ISO format strings
        dt = dt.replace("Z", "+00:00")
        try:
            parsed_dt = datetime.fromisoformat(dt)
        except ValueError as e:
            raise ValueError(f"Cannot parse datetime string: {dt}") from e
        dt = parsed_dt

    if isinstance(dt, datetime):
        if dt.tzinfo is None:
            # Assume UTC for naive datetimes
            return dt.replace(tzinfo=UTC)
        return dt

    raise ValueError(f"Unsupported datetime type: {type(dt)}")
```

#### B1: Synchronization Service Updates

**File:** `server/src/core/services/general/synchronization_service.py` **Lines:** 1465-1472

**Current Code:**

```python
# Handle string timestamps by parsing them
if isinstance(local_timestamp, str):
    local_timestamp = datetime.fromisoformat(
        local_timestamp.replace("Z", "+00:00")
    )
if isinstance(central_timestamp, str):
    central_timestamp = datetime.fromisoformat(
        central_timestamp.replace("Z", "+00:00")
    )
```

**Fixed Code:**

```python
# Handle string timestamps by parsing them with timezone awareness
from src.core.utils.datetime_utils import ensure_timezone_aware

local_timestamp = ensure_timezone_aware(local_timestamp)
central_timestamp = ensure_timezone_aware(central_timestamp)

if local_timestamp is None or central_timestamp is None:
    raise SynchronizationError("Invalid timestamp data for comparison")
```

### Batch C: AsyncMock Configuration Specifications

#### C1: Standardized AsyncMock Fixtures

**File:** `server/tests/core/services/conftest.py`

**New Fixture to Add:**

```python
@pytest.fixture
async def mock_user_repository():
    """Create a properly configured async mock for UserRepository."""
    mock_repo = AsyncMock(spec=UserRepository)

    # Configure common async methods
    mock_repo.create = AsyncMock()
    mock_repo.get_by_id = AsyncMock()
    mock_repo.get_by_email = AsyncMock()
    mock_repo.update = AsyncMock()
    mock_repo.delete = AsyncMock()
    mock_repo.check_email_exists = AsyncMock()

    # Configure session methods
    mock_repo.db_session = AsyncMock()
    mock_repo.db_session.flush = AsyncMock()
    mock_repo.db_session.commit = AsyncMock()
    mock_repo.db_session.refresh = AsyncMock()

    return mock_repo

@pytest.fixture
async def mock_user_service(mock_user_repository):
    """Create UserService with properly mocked dependencies."""
    with patch('src.core.repositories.general.user_repository.UserRepository') as mock_repo_class:
        mock_repo_class.return_value = mock_user_repository

        # Create service instance
        service = UserService(mock_user_repository)
        yield service
```

#### C1: UserService Test Updates

**File:** `server/tests/core/services/test_user_service.py`

**Pattern for Async Test Methods:**

```python
async def test_create_user_success(self, mock_user_service, mock_user_repository):
    """Test successful user creation with proper async mocking."""
    # Setup test data
    user_data = UserCreateSchema(
        name="Test User",
        email="<EMAIL>",
        password="SecurePassword123!"
    )

    # Configure mock returns
    mock_user_repository.check_email_exists.return_value = False
    mock_user_repository.create.return_value = User(
        id=1,
        name=user_data.name,
        email=user_data.email,
        is_active=True
    )

    # Execute test
    result = await mock_user_service.create_user(user_data)

    # Verify results
    assert result.name == user_data.name
    assert result.email == user_data.email
    mock_user_repository.create.assert_called_once()
    mock_user_repository.db_session.flush.assert_called_once()
```

### Batch D: Test Data Factory Specifications

#### D1: Comprehensive Factory Implementation

**File:** `server/tests/factories/model_factories.py` (New File)

**Factory Base Pattern:**

```python
import factory
from datetime import datetime
from src.core.models.general.task import Task
from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.enums import TaskPriority, TaskStatus

class UserFactory(factory.Factory):
    class Meta:
        model = User

    name = factory.Sequence(lambda n: f"Test User {n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.name.lower().replace(' ', '.')}@example.com")
    is_active = True
    is_superuser = False
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

class ProjectFactory(factory.Factory):
    class Meta:
        model = Project

    name = factory.Sequence(lambda n: f"Test Project {n}")
    project_number = factory.Sequence(lambda n: f"PROJ-{n:04d}")
    description = factory.LazyAttribute(lambda obj: f"Description for {obj.name}")
    status = "active"
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

class TaskFactory(factory.Factory):
    class Meta:
        model = Task

    name = factory.Sequence(lambda n: f"Test Task {n}")
    description = factory.LazyAttribute(lambda obj: f"Description for {obj.name}")
    project_id = factory.SubFactory(ProjectFactory)
    priority = TaskPriority.MEDIUM
    status = TaskStatus.NOT_STARTED
    is_deleted = False
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)
```

---

## Monitoring and Validation Framework

### Per-Batch Validation Commands

**Repository Layer (Batches A, D):**

```bash
# Run specific repository tests
uv run pytest tests/core/repositories/ -v --tb=short

# Check for database constraint violations
uv run pytest tests/core/models/ -v --tb=short

# Validate query performance
uv run pytest tests/performance/test_database_performance.py -v
```

**Service Layer (Batches B, C):**

```bash
# Run service tests with async validation
uv run pytest tests/core/services/ -v --tb=short

# Check datetime handling
uv run pytest tests/core/services/test_synchronization_service_conflict_resolution.py -v

# Validate async patterns
uv run pytest tests/core/services/test_user_service.py -v
```

**Integration Layer (Batches E, F):**

```bash
# Run integration tests
uv run pytest tests/integration/ -v --tb=short

# Check connection manager
uv run pytest tests/core/database/test_connection_manager_integration.py -v

# Full test suite validation
uv run pytest tests/ --html=test-report-all.html --self-contained-html
```

### Quality Gate Automation

**Pre-Batch Execution:**

```bash
#!/bin/bash
# quality_gate_check.sh
echo "Running quality gates..."

# Type checking
uv run mypy src/ --show-error-codes || exit 1

# Linting
uv run ruff check . || exit 1

# Format checking
uv run ruff format . --check || exit 1

echo "Quality gates passed!"
```

**Post-Batch Validation:**

```bash
#!/bin/bash
# batch_validation.sh
BATCH_NAME=$1

echo "Validating batch: $BATCH_NAME"

# Run quality gates
./quality_gate_check.sh || exit 1

# Run relevant tests based on batch
case $BATCH_NAME in
    "A"|"D")
        uv run pytest tests/core/repositories/ tests/core/models/ -v
        ;;
    "B"|"C")
        uv run pytest tests/core/services/ -v
        ;;
    "E")
        uv run pytest tests/core/database/ -v
        ;;
    "F")
        uv run pytest tests/ --tb=short
        ;;
esac

echo "Batch $BATCH_NAME validation complete!"
```

This implementation plan provides a systematic, risk-managed approach to resolving all identified test failures while
maintaining the project's engineering-grade quality standards.
